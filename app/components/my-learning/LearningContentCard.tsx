import { useState } from 'react';
import { Link } from 'react-router';
import { 
  <PERSON><PERSON><PERSON>, 
  Clock, 
  Eye, 
  EyeOff, 
  MoreVertical, 
  Share2, 
  Trash2, 
  Play,
  CheckCircle,
  Circle,
  Tag
} from 'lucide-react';
import { Card, CardContent, CardHeader } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Progress } from '~/components/ui/progress';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu';
import type { LearningContentWithProgress } from '~/db/services/learning-content';

interface LearningContentCardProps {
  content: LearningContentWithProgress;
  viewMode?: 'grid' | 'list';
  onDelete?: (id: string) => void;
  onShare?: (content: LearningContentWithProgress) => void;
  onTogglePublic?: (id: string) => void;
  className?: string;
}

export function LearningContentCard({
  content,
  viewMode = 'grid',
  onDelete,
  onShare,
  onTogglePublic,
  className = '',
}: LearningContentCardProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const progressPercentage = content.progress?.completionPercentage || 0;
  const isCompleted = content.progress?.isCompleted || false;
  const hasStarted = content.progress !== null;

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'intermediate':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300';
      case 'advanced':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  const getContentTypeColor = (type: string) => {
    switch (type) {
      case 'kwaci-primer':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300';
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
    }
  };

  const formatReadingTime = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  const getProgressIcon = () => {
    if (isCompleted) {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    }
    if (hasStarted) {
      return <Play className="h-4 w-4 text-blue-600" />;
    }
    return <Circle className="h-4 w-4 text-gray-400" />;
  };

  const getProgressText = () => {
    if (isCompleted) return 'Completed';
    if (hasStarted) return `${progressPercentage}% complete`;
    return 'Not started';
  };

  if (viewMode === 'list') {
    return (
      <Card className={`hover:shadow-md transition-shadow ${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between gap-4">
            {/* Content Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-2">
                <Link 
                  to={`/dashboard/learn/${content.id}`}
                  className="font-semibold text-lg hover:text-blue-600 dark:hover:text-blue-400 transition-colors truncate"
                >
                  {content.title}
                </Link>
                <div className="flex items-center gap-1 flex-shrink-0">
                  {content.isPublic ? (
                    <Eye className="h-4 w-4 text-green-600" />
                  ) : (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  )}
                </div>
              </div>
              
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">
                {content.description}
              </p>
              
              <div className="flex items-center gap-2 flex-wrap">
                <Badge className={getLevelColor(content.learningLevel)}>
                  {content.learningLevel}
                </Badge>
                <Badge className={getContentTypeColor(content.contentType)}>
                  {content.contentType === 'kwaci-primer' ? 'KWACI Primer' : 'Standard'}
                </Badge>
                <div className="flex items-center gap-1 text-sm text-gray-500">
                  <Clock className="h-3 w-3" />
                  {formatReadingTime(content.estimatedReadingTime)}
                </div>
              </div>
            </div>

            {/* Progress */}
            <div className="flex items-center gap-4 flex-shrink-0">
              <div className="text-right min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  {getProgressIcon()}
                  <span className="text-sm font-medium">{getProgressText()}</span>
                </div>
                {hasStarted && (
                  <Progress value={progressPercentage} className="w-24" />
                )}
              </div>

              {/* Actions */}
              <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen}>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onShare?.(content)}>
                    <Share2 className="h-4 w-4 mr-2" />
                    Share
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onTogglePublic?.(content.id)}>
                    {content.isPublic ? (
                      <>
                        <EyeOff className="h-4 w-4 mr-2" />
                        Make Private
                      </>
                    ) : (
                      <>
                        <Eye className="h-4 w-4 mr-2" />
                        Make Public
                      </>
                    )}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => onDelete?.(content.id)}
                    className="text-red-600 dark:text-red-400"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Grid view
  return (
    <Card className={`hover:shadow-lg transition-all duration-200 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-2">
          <div className="flex-1 min-w-0">
            <Link 
              to={`/dashboard/learn/${content.id}`}
              className="font-semibold text-lg hover:text-blue-600 dark:hover:text-blue-400 transition-colors line-clamp-2"
            >
              {content.title}
            </Link>
          </div>
          
          <div className="flex items-center gap-1 flex-shrink-0">
            {content.isPublic ? (
              <Eye className="h-4 w-4 text-green-600" />
            ) : (
              <EyeOff className="h-4 w-4 text-gray-400" />
            )}
            
            <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onShare?.(content)}>
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onTogglePublic?.(content.id)}>
                  {content.isPublic ? (
                    <>
                      <EyeOff className="h-4 w-4 mr-2" />
                      Make Private
                    </>
                  ) : (
                    <>
                      <Eye className="h-4 w-4 mr-2" />
                      Make Public
                    </>
                  )}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => onDelete?.(content.id)}
                  className="text-red-600 dark:text-red-400"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
          {content.description}
        </p>

        {/* Progress */}
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2">
            {getProgressIcon()}
            <span className="text-sm font-medium">{getProgressText()}</span>
          </div>
          {hasStarted && (
            <Progress value={progressPercentage} className="h-2" />
          )}
        </div>

        {/* Metadata */}
        <div className="flex items-center justify-between gap-2 mb-3">
          <div className="flex items-center gap-1 text-sm text-gray-500">
            <Clock className="h-3 w-3" />
            {formatReadingTime(content.estimatedReadingTime)}
          </div>
          <div className="flex items-center gap-1 text-sm text-gray-500">
            <BookOpen className="h-3 w-3" />
            {content.steps?.length || 0} steps
          </div>
        </div>

        {/* Badges */}
        <div className="flex items-center gap-2 flex-wrap">
          <Badge className={getLevelColor(content.learningLevel)}>
            {content.learningLevel}
          </Badge>
          <Badge className={getContentTypeColor(content.contentType)}>
            {content.contentType === 'kwaci-primer' ? 'KWACI' : 'Standard'}
          </Badge>
        </div>

        {/* Tags */}
        {content.tags && content.tags.length > 0 && (
          <div className="flex items-center gap-1 mt-3 flex-wrap">
            <Tag className="h-3 w-3 text-gray-400" />
            {content.tags.slice(0, 3).map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {content.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{content.tags.length - 3}
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
