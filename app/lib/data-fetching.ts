import { use<PERSON><PERSON><PERSON>, useLoaderD<PERSON>, redirect } from 'react-router';
import type { LoaderFunctionArgs, ActionFunctionArgs } from 'react-router';
import { useQuery, useMutation, type UseQueryOptions, type UseMutationOptions } from '@tanstack/react-query';
import { handleRouterError, handleQueryError, createAppError, ERROR_CODES } from './error-handling';
import type { AppError } from './error-handling';

// Types for data fetching with proper generics (no default any)
export interface RouterLoaderData<T> {
  data: T;
  error?: AppError;
  meta?: {
    timestamp: string;
    version?: string;
    [key: string]: unknown;
  };
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: AppError;
  meta?: {
    timestamp: string;
    requestId?: string;
    [key: string]: unknown;
  };
}

// React Router loader wrapper with error handling
export async function createRouterLoader<T>(
  handler: (args: LoaderFunctionArgs) => Promise<T>
) {
  return async (args: LoaderFunctionArgs) => {
    try {
      const data = await handler(args);
      return Response.json({
        data,
        meta: {
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      console.error('Loader error:', error);
      
      // Handle authentication errors by redirecting
      if (error instanceof Response && (error.status === 401 || error.status === 403)) {
        throw error;
      }
      
      // For other errors, return error data instead of throwing
      const appError = handleQueryError(error);
      return Response.json(
        {
          data: null as T,
          error: appError,
          meta: {
            timestamp: new Date().toISOString(),
          },
        },
        { status: appError.statusCode || 500 }
      );
    }
  };
}

// React Router action wrapper with error handling
export async function createRouterAction<T>(
  handler: (args: ActionFunctionArgs) => Promise<T>
) {
  return async (args: ActionFunctionArgs) => {
    try {
      const data = await handler(args);
      return Response.json({
        success: true,
        data,
        meta: {
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      console.error('Action error:', error);
      handleRouterError(error);
    }
  };
}

// Hook to use React Router loader data with error handling
export function useRouterLoaderData<T>(): {
  data: T | null;
  error: AppError | null;
  isLoading: boolean;
  meta?: RouterLoaderData<T>['meta'];
} {
  const loaderData = useLoaderData<RouterLoaderData<T>>();
  
  return {
    data: loaderData.data,
    error: loaderData.error || null,
    isLoading: false, // React Router loaders are always loaded on route entry
    meta: loaderData.meta,
  };
}

// Enhanced fetcher hook with error handling
export function useRouterFetcher<T>() {
  const fetcher = useFetcher<ApiResponse<T>>();
  
  return {
    ...fetcher,
    data: fetcher.data?.data || null,
    error: fetcher.data?.error || null,
    isLoading: fetcher.state === 'loading' || fetcher.state === 'submitting',
    isSuccess: fetcher.data?.success || false,
    meta: fetcher.data?.meta,
  };
}

// Hybrid query hook that combines React Router and React Query
export function useHybridQuery<T>(
  key: string[],
  fetcher: () => Promise<T>,
  options?: Omit<UseQueryOptions<T, AppError>, 'queryKey' | 'queryFn'> & {
    useRouterData?: boolean;
    routerData?: T;
  }
) {
  const { useRouterData = false, routerData, ...queryOptions } = options || {};
  
  return useQuery<T, AppError>({
    queryKey: key,
    queryFn: async () => {
      try {
        return await fetcher();
      } catch (error) {
        throw handleQueryError(error);
      }
    },
    initialData: useRouterData ? routerData : undefined,
    staleTime: useRouterData ? 0 : 5 * 60 * 1000, // 5 minutes for non-Router data
    ...queryOptions,
  });
}

// Hybrid mutation hook with error handling
export function useHybridMutation<TData, TVariables>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: Omit<UseMutationOptions<TData, AppError, TVariables>, 'mutationFn'>
) {
  return useMutation<TData, AppError, TVariables>({
    mutationFn: async (variables) => {
      try {
        return await mutationFn(variables);
      } catch (error) {
        throw handleQueryError(error);
      }
    },
    ...options,
  });
}

// API client for React Query
export class ApiClient {
  private baseUrl: string;
  
  constructor(baseUrl: string = '/api') {
    this.baseUrl = baseUrl;
  }
  
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });
    
    if (!response.ok) {
      throw response;
    }
    
    const data = await response.json();
    
    // Handle different response formats
    if (data.hasOwnProperty('success')) {
      if (!data.success) {
        throw createAppError(
          data.error?.code || ERROR_CODES.SERVER_ERROR,
          data.error?.message || 'API request failed',
          data.error?.details,
          response.status
        );
      }
      // Return the data field if it exists, otherwise return the whole response
      return data.data !== undefined ? data.data : data;
    }
    
    // For responses that don't have a success field, return the data directly
    return data;
  }
  
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    let fullEndpoint = endpoint;
    
    if (params) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
      const queryString = searchParams.toString();
      if (queryString) {
        fullEndpoint += (endpoint.includes('?') ? '&' : '?') + queryString;
      }
    }
    
    return this.request<T>(fullEndpoint);
  }
  
  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }
  
  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }
  
  async patch<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }
  
  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
    });
  }
}

// Import types for endpoint-specific methods
import type {
  LearningContent,
  NewLearningContent,
  LearningContentListResponse,
  LearningContentStatsResponse
} from '~/db/schema/learning-content';

// Typed API client with endpoint-specific methods
export class TypedApiClient extends ApiClient {
  // Learning Content endpoints
  async getLearningContent(id: string): Promise<LearningContent> {
    return this.get<LearningContent>(`/learning/${id}`);
  }

  async getAllLearningContent(params?: {
    contentType?: 'standard' | 'kwaci-primer';
    learningLevel?: 'beginner' | 'intermediate' | 'advanced';
    limit?: number;
    offset?: number;
  }): Promise<LearningContentListResponse> {
    return this.get<LearningContentListResponse>('/learning', params);
  }

  async searchLearningContent(params: {
    query: string;
    contentType?: 'standard' | 'kwaci-primer';
    learningLevel?: 'beginner' | 'intermediate' | 'advanced';
    limit?: number;
    offset?: number;
  }): Promise<LearningContentListResponse> {
    return this.get<LearningContentListResponse>('/learning', params);
  }

  async createLearningContent(data: Omit<NewLearningContent, 'id' | 'createdAt' | 'updatedAt' | 'userId'>): Promise<LearningContent> {
    return this.post<LearningContent>('/learning', data);
  }

  async updateLearningContent(id: string, data: Partial<Omit<NewLearningContent, 'id' | 'createdAt' | 'updatedAt' | 'userId'>>): Promise<LearningContent> {
    return this.put<LearningContent>(`/learning/${id}`, data);
  }

  async deleteLearningContent(id: string): Promise<{ success: true }> {
    return this.delete<{ success: true }>(`/learning/${id}`);
  }

  async getLearningContentStats(): Promise<LearningContentStatsResponse> {
    return this.get<LearningContentStatsResponse>('/learning/stats');
  }

  // Search and Query methods
  async searchContent(searchTerm: string): Promise<any> {
    return this.get<any>(`/search?q=${encodeURIComponent(searchTerm)}&type=content`);
  }

  // Quiz methods
  async getQuizProgress(quizId: string): Promise<any> {
    return this.get<any>(`/quiz/${quizId}/progress`);
  }

  async getQuizAnalytics(quizId: string): Promise<any> {
    return this.get<any>(`/quiz/${quizId}/analytics`);
  }

  async submitQuizAnswer(quizId: string, data: any): Promise<any> {
    return this.post<any>(`/quiz/${quizId}/answer`, data);
  }

  async updateQuiz(quizId: string, data: any): Promise<any> {
    return this.put<any>(`/quiz/${quizId}`, data);
  }

  // Chat methods
  async getChatHistory(chatId: string): Promise<any> {
    return this.get<any>(`/chat/${chatId}/history`);
  }

  async createChat(data: any): Promise<any> {
    return this.post<any>('/chat', data);
  }

  // Progress methods
  async getProgress(userId?: string): Promise<any> {
    const endpoint = userId ? `/progress?userId=${userId}` : '/progress';
    return this.get<any>(endpoint);
  }

  async updateProgress(data: any): Promise<any> {
    return this.post<any>('/progress', data);
  }

  async updateLearningProgress(data: any): Promise<any> {
    return this.post<any>('/learning/progress', data);
  }

  // Analytics methods
  async getLearningAnalytics(contentId: string, userId?: string): Promise<any> {
    const endpoint = userId ? `/learning/${contentId}/analytics?userId=${userId}` : `/learning/${contentId}/analytics`;
    return this.get<any>(endpoint);
  }

  // Content Generation methods
  async generateContent(data: {
    topic: string;
    learningLevel: 'beginner' | 'intermediate' | 'advanced';
    preferredContentTypes: string[];
    additionalContext?: string;
    contentType?: string;
  }): Promise<any> {
    return this.post<any>('/content/generate', data);
  }
}

// Default API client instance
export const apiClient = new TypedApiClient();

// Utility to create query keys
export function createQueryKey(base: string, params?: Record<string, any>): string[] {
  const key = [base];
  
  if (params) {
    // Sort params for consistent keys
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((acc, k) => {
        if (params[k] !== undefined && params[k] !== null) {
          acc[k] = params[k];
        }
        return acc;
      }, {} as Record<string, any>);
    
    if (Object.keys(sortedParams).length > 0) {
      key.push(JSON.stringify(sortedParams));
    }
  }
  
  return key;
}

// Utility to invalidate related queries
export function createInvalidationPattern(base: string): string[] {
  return [base];
}

// Server-side typed fetch functions for loaders and actions
export class ServerApiClient {
  /**
   * Typed fetch wrapper for server-side operations
   * Handles cookies and proper error handling for React Router loaders/actions
   */
  static async typedFetch<T>(
    request: Request,
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = new URL(endpoint, request.url);

    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        Cookie: request.headers.get('Cookie') || '',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      if (response.status === 404) {
        throw new Response('Resource not found', { status: 404 });
      }
      throw new Response(`Failed to fetch: ${response.statusText}`, { status: response.status });
    }

    const result = await response.json();

    if (!result.success) {
      throw new Response(result.error || 'API request failed', { status: 500 });
    }

    return result.data as T;
  }

  // Learning Content server-side methods
  static async getLearningContent(request: Request, id: string): Promise<LearningContent> {
    return this.typedFetch<LearningContent>(request, `/api/learning/${id}`);
  }

  static async getAllLearningContent(
    request: Request,
    params?: {
      contentType?: 'standard' | 'kwaci-primer';
      learningLevel?: 'beginner' | 'intermediate' | 'advanced';
      limit?: number;
      offset?: number;
    }
  ): Promise<LearningContentListResponse> {
    const url = new URL('/api/learning', request.url);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value));
        }
      });
    }
    return this.typedFetch<LearningContentListResponse>(request, url.pathname + url.search);
  }

  static async searchLearningContent(
    request: Request,
    params: {
      query: string;
      contentType?: 'standard' | 'kwaci-primer';
      learningLevel?: 'beginner' | 'intermediate' | 'advanced';
      limit?: number;
      offset?: number;
    }
  ): Promise<LearningContentListResponse> {
    const url = new URL('/api/learning', request.url);
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.append(key, String(value));
      }
    });
    return this.typedFetch<LearningContentListResponse>(request, url.pathname + url.search);
  }

  static async getLearningContentStats(request: Request): Promise<LearningContentStatsResponse> {
    return this.typedFetch<LearningContentStatsResponse>(request, '/api/learning/stats');
  }

  // Content Generation methods
  static async generateContent(
    request: Request,
    data: {
      topic: string;
      learningLevel: 'beginner' | 'intermediate' | 'advanced';
      preferredContentTypes?: string[];
      additionalContext?: string;
      contentType?: string;
    }
  ): Promise<any> {
    const url = new URL('/api/content/generate', request.url);
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: request.headers.get('Cookie') || '',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      if (response.status === 404) {
        throw new Response('Content generation endpoint not found', { status: 404 });
      }
      throw new Response(`Failed to generate content: ${response.statusText}`, { status: response.status });
    }

    const result = await response.json();

    if (!result.success) {
      throw new Response(result.error || 'Content generation failed', { status: 500 });
    }

    return result.data;
  }

  static async saveLearningContent(
    request: Request,
    data: {
      title: string;
      description?: string;
      content: any;
      contentType: 'standard' | 'kwaci-primer';
      learningLevel?: 'beginner' | 'intermediate' | 'advanced';
      level?: string;
      tags?: string[];
      estimatedReadingTime?: number;
      isPublic?: boolean;
      aiMetadata?: any;
    }
  ): Promise<LearningContent> {
    const url = new URL('/api/learning', request.url);
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: request.headers.get('Cookie') || '',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      if (response.status === 404) {
        throw new Response('Learning content endpoint not found', { status: 404 });
      }
      throw new Response(`Failed to save learning content: ${response.statusText}`, { status: response.status });
    }

    const result = await response.json();

    if (!result.success) {
      throw new Response(result.error || 'Failed to save learning content', { status: 500 });
    }

    return result.data as LearningContent;
  }
}

// Type-safe form data parser
export async function parseFormData<T extends Record<string, any>>(
  request: Request,
  schema?: (data: any) => T
): Promise<T> {
  const formData = await request.formData();
  const data = Object.fromEntries(formData.entries());
  
  if (schema) {
    return schema(data);
  }
  
  return data as T;
}

// JSON body parser with validation
export async function parseJsonBody<T>(
  request: Request,
  schema?: (data: any) => T
): Promise<T> {
  const data = await request.json();
  
  if (schema) {
    return schema(data);
  }
  
  return data as T;
}